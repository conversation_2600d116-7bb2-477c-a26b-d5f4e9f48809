---
import { type CollectionEntry, getCollection } from "astro:content";
import Pagination from "@/components/Paginator.astro";
import TimelineNote from "@/components/note/TimelineNote.astro";
import PageLayout from "@/layouts/Base.astro";
import { collectionDateSort } from "@/utils/date";
import type { GetStaticPaths, Page } from "astro";
import { Icon } from "astro-icon/components";

export const getStaticPaths = (async ({ paginate }) => {
	const MAX_NOTES_PER_PAGE = 10;
	const allNotes = await getCollection("note");
	return paginate(allNotes.sort(collectionDateSort), { pageSize: MAX_NOTES_PER_PAGE });
}) satisfies GetStaticPaths;

interface Props {
	page: Page<CollectionEntry<"note">>;
	uniqueTags: string[];
}

const { page } = Astro.props;

const meta = {
	description: "Read my collection of notes",
	title: "Notes",
};

const paginationProps = {
	...(page.url.prev && {
		prevUrl: {
			text: "← Previous Page",
			url: page.url.prev,
		},
	}),
	...(page.url.next && {
		nextUrl: {
			text: "Next Page →",
			url: page.url.next,
		},
	}),
};
---

<PageLayout meta={meta}>
	<section>
		<h1 class="title mb-12 flex items-center gap-3">
			Notes <a class="text-accent" href="/notes/rss.xml" target="_blank">
				<span class="sr-only">RSS feed</span>
				<Icon aria-hidden="true" class="h-6 w-6" focusable="false" name="mdi:rss" />
			</a>
		</h1>
		<!-- 垂直时间线容器 -->
		<div class="timeline-container">
			<!-- 时间线主线 -->
			<div class="timeline-line absolute left-6 top-8 bottom-0 w-0.5"></div>

			<!-- 时间线开始标记 -->
			<div class="timeline-start flex items-center gap-4 mb-8">
				<div class="w-3 h-3 bg-accent rounded-full border-2 border-global-bg shadow-sm relative z-10"></div>
				<span class="text-sm text-accent font-medium">时间线开始</span>
			</div>

			<ul class="timeline-list">
				{
					page.data.map((note) => (
						<li class="timeline-item">
							<!-- 时间线节点 -->
							<div class="timeline-node">
								<div class="timeline-dot w-3 h-3 rounded-full"></div>
							</div>

							<!-- 内容区域 -->
							<div class="timeline-content">
								<TimelineNote note={note} as="h2" isPreview />
							</div>
						</li>
					))
				}
			</ul>

			<!-- 时间线结束标记 -->
			<div class="timeline-end flex items-center gap-4 mt-8">
				<div class="w-3 h-3 bg-accent/50 rounded-full border-2 border-global-bg shadow-sm relative z-10"></div>
				<span class="text-sm text-accent/70 font-medium">更多内容...</span>
			</div>
		</div>
		<Pagination {...paginationProps} />
	</section>
</PageLayout>
