---
import { type CollectionEntry, getCollection } from "astro:content";
// import SocialList from "@/components/SocialList.astro";
import PostPreview from "@/components/blog/PostPreview.astro";
import TimelineNote from "@/components/note/TimelineNote.astro";
import { getAllPosts } from "@/data/post";
import PageLayout from "@/layouts/Base.astro";
import { collectionDateSort } from "@/utils/date";

// Posts
const MAX_POSTS = 10;
const allPosts = await getAllPosts();
const allPostsByDate = allPosts
	.sort(collectionDateSort)
	.slice(0, MAX_POSTS) as CollectionEntry<"post">[];

// Pinned Posts, set to a max of 3;
const MAX_PINNED_POSTS = 3;
const pinnedPosts = allPostsByDate
	.filter((p) => p.data.pinned)
	.slice(0, MAX_PINNED_POSTS);

// Notes
const MAX_NOTES = 5;
const allNotes = await getCollection("note");
const latestNotes = allNotes.sort(collectionDateSort).slice(0, MAX_NOTES);
---

<PageLayout meta={{ title: "Home" }}>
	<section>
		<h1 class="title mb-6">Hello!</h1>
		<p class="mb-4">I'm Moatkon,This is my blog.</p>
		<!-- <p class="mb-4">这里主要是记录生活,会探索如何做生意,毕竟自己不是顶级打工人,靠着公司不是长久之计。</p> -->
		<blockquote>- 2025-08-03</blockquote>

		<!-- <SocialList />  -->
	</section>
	{
		pinnedPosts.length > 0 && (
			<section class="mt-16">
				<h2 class="title text-accent mb-6 text-xl">Pinned Posts</h2>
				<ul class="space-y-4" role="list">
					{pinnedPosts.map((p) => (
						<li class="grid gap-1 sm:grid-cols-[auto_1fr]">
							<PostPreview post={p} />
						</li>
					))}
				</ul>
			</section>
		)
	}
	<section class="mt-16">
		<h2 class="title text-accent mb-6 text-xl">
			<a href="/posts/">Posts</a>
		</h2>
		<ul class="space-y-4" role="list">
			{
				allPostsByDate.map((p) => (
					<li class="grid gap-1 sm:grid-cols-[auto_1fr]">
						<PostPreview post={p} />
					</li>
				))
			}
		</ul>
	</section>
	{
		latestNotes.length > 0 && (
			<section class="mt-16">
				<h2 class="title text-accent mb-6 text-xl">
					<a href="/notes/">Notes</a>
				</h2>
				<!-- 首页时间线样式（简化版） -->
				<div class="timeline-container">
					<div class="timeline-line absolute left-6 top-8 bottom-0 w-0.5"></div>

					<!-- 时间线开始标记 -->
					<div class="timeline-start flex items-center gap-4 mb-6">
						<div class="w-3 h-3 bg-accent rounded-full border-2 border-global-bg shadow-sm relative z-10"></div>
						<span class="text-sm text-accent font-medium">最新动态</span>
					</div>

					<ul class="timeline-list" role="list">
						{latestNotes.map((note) => (
							<li class="timeline-item">
								<div class="timeline-node">
									<div class="timeline-dot w-3 h-3 rounded-full"></div>
								</div>
								<div class="timeline-content">
									<TimelineNote note={note} as="h3" isPreview />
								</div>
							</li>
						))}
					</ul>

					<!-- 时间线结束标记 -->
					<div class="timeline-end flex items-center gap-4 mt-6">
						<div class="w-3 h-3 bg-accent/50 rounded-full border-2 border-global-bg shadow-sm relative z-10"></div>
						<a href="/notes/" class="text-sm text-accent hover:text-accent-2 font-medium transition-colors">
							查看全部 →
						</a>
					</div>
				</div>
			</section>
		)
	}
</PageLayout>
